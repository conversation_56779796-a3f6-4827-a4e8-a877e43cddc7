<template>
  <div class="safe-test-page">
    <h1>安全测试页面</h1>
    <p>这是一个基本的测试页面，用于验证路由和内容显示。</p>
    
    <div class="test-section">
      <h2>基本功能测试</h2>
      <ul>
        <li>Vue 3 组件渲染 ✓</li>
        <li>路由导航 ✓</li>
        <li>CSS 样式 ✓</li>
        <li>JavaScript 执行 ✓</li>
      </ul>
    </div>

    <div class="interactive-section">
      <h2>交互测试</h2>
      <button @click="handleClick" class="test-button">
        点击测试 (点击次数: {{ clickCount }})
      </button>
      
      <input 
        v-model="inputValue" 
        placeholder="输入测试文本" 
        class="test-input"
      />
      
      <p v-if="inputValue">输入的内容: {{ inputValue }}</p>
    </div>

    <div class="error-test-section">
      <h2>错误处理测试</h2>
      <button @click="triggerError" class="error-button">
        触发测试错误
      </button>
      <div v-if="errorMessage" class="error-display">
        错误信息: {{ errorMessage }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const clickCount = ref(0)
const inputValue = ref('')
const errorMessage = ref('')

const handleClick = () => {
  clickCount.value++
  console.log('按钮被点击，次数:', clickCount.value)
}

const triggerError = () => {
  try {
    // 故意触发一个错误进行测试
    const obj: any = null
    obj.someMethod()
  } catch (error) {
    errorMessage.value = (error as Error).message
    console.error('测试错误:', error)
  }
}
</script>

<style scoped>
.safe-test-page {
  padding: 2rem;
  max-width: 800px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

h1 {
  color: #2563eb;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 0.5rem;
}

h2 {
  color: #374151;
  margin-top: 2rem;
}

.test-section {
  background: #f9fafb;
  padding: 1.5rem;
  border-radius: 8px;
  margin: 1rem 0;
}

.test-section ul {
  margin: 0;
  padding-left: 1.5rem;
}

.test-section li {
  margin: 0.5rem 0;
  color: #059669;
}

.interactive-section {
  background: #fef3c7;
  padding: 1.5rem;
  border-radius: 8px;
  margin: 1rem 0;
}

.test-button {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  margin: 0.5rem;
  transition: background-color 0.2s;
}

.test-button:hover {
  background: #2563eb;
}

.test-input {
  display: block;
  width: 100%;
  max-width: 300px;
  padding: 0.75rem;
  border: 2px solid #d1d5db;
  border-radius: 6px;
  font-size: 1rem;
  margin: 0.5rem 0;
}

.test-input:focus {
  outline: none;
  border-color: #3b82f6;
}

.error-test-section {
  background: #fee2e2;
  padding: 1.5rem;
  border-radius: 8px;
  margin: 1rem 0;
}

.error-button {
  background: #dc2626;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  margin: 0.5rem;
}

.error-button:hover {
  background: #b91c1c;
}

.error-display {
  background: #fecaca;
  border: 1px solid #f87171;
  padding: 1rem;
  border-radius: 4px;
  margin-top: 1rem;
  color: #dc2626;
  font-family: monospace;
}
</style>