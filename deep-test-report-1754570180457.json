{"timestamp": "2025-08-07T12:35:40.496Z", "totalTests": 12, "passedTests": 12, "failedTests": 0, "errors": [{"type": "console_error", "message": "🚨 页面错误: JSHandle@error", "timestamp": "2025-08-07T12:35:42.817Z"}, {"type": "page_error", "message": "Cannot read properties of undefined (reading 'on')", "stack": "TypeError: Cannot read properties of undefined (reading 'on')\n    at <anonymous> (http://localhost:5174/src/App.vue:25:17)", "timestamp": "2025-08-07T12:35:42.818Z"}, {"type": "console_error", "message": "🚨 页面错误: JSHandle@error", "timestamp": "2025-08-07T12:35:44.294Z"}, {"type": "page_error", "message": "Cannot read properties of undefined (reading 'on')", "stack": "TypeError: Cannot read properties of undefined (reading 'on')\n    at <anonymous> (http://localhost:5174/src/App.vue:25:17)", "timestamp": "2025-08-07T12:35:44.295Z"}, {"type": "console_error", "message": "🚨 页面错误: JSHandle@error", "timestamp": "2025-08-07T12:35:48.365Z"}, {"type": "page_error", "message": "Cannot read properties of undefined (reading 'on')", "stack": "TypeError: Cannot read properties of undefined (reading 'on')\n    at <anonymous> (http://localhost:5174/src/App.vue:25:17)", "timestamp": "2025-08-07T12:35:48.366Z"}, {"type": "console_error", "message": "🚨 页面错误: JSHandle@error", "timestamp": "2025-08-07T12:35:52.226Z"}, {"type": "page_error", "message": "Cannot read properties of undefined (reading 'on')", "stack": "TypeError: Cannot read properties of undefined (reading 'on')\n    at <anonymous> (http://localhost:5174/src/App.vue:25:17)", "timestamp": "2025-08-07T12:35:52.227Z"}, {"type": "console_error", "message": "🚨 页面错误: JSHandle@error", "timestamp": "2025-08-07T12:35:56.065Z"}, {"type": "page_error", "message": "Cannot read properties of undefined (reading 'on')", "stack": "TypeError: Cannot read properties of undefined (reading 'on')\n    at <anonymous> (http://localhost:5174/src/App.vue:25:17)", "timestamp": "2025-08-07T12:35:56.066Z"}, {"type": "console_error", "message": "🚨 页面错误: JSHandle@error", "timestamp": "2025-08-07T12:35:59.859Z"}, {"type": "page_error", "message": "Cannot read properties of undefined (reading 'on')", "stack": "TypeError: Cannot read properties of undefined (reading 'on')\n    at <anonymous> (http://localhost:5174/src/App.vue:25:17)", "timestamp": "2025-08-07T12:35:59.860Z"}, {"type": "console_error", "message": "🚨 页面错误: JSHandle@error", "timestamp": "2025-08-07T12:36:03.710Z"}, {"type": "page_error", "message": "Cannot read properties of undefined (reading 'on')", "stack": "TypeError: Cannot read properties of undefined (reading 'on')\n    at <anonymous> (http://localhost:5174/src/App.vue:25:17)", "timestamp": "2025-08-07T12:36:03.711Z"}, {"type": "console_error", "message": "🚨 页面错误: JSHandle@error", "timestamp": "2025-08-07T12:36:07.786Z"}, {"type": "page_error", "message": "Cannot read properties of undefined (reading 'on')", "stack": "TypeError: Cannot read properties of undefined (reading 'on')\n    at <anonymous> (http://localhost:5174/src/App.vue:25:17)", "timestamp": "2025-08-07T12:36:07.788Z"}, {"type": "console_error", "message": "🚨 页面错误: JSHandle@error", "timestamp": "2025-08-07T12:36:11.876Z"}, {"type": "page_error", "message": "Cannot read properties of undefined (reading 'on')", "stack": "TypeError: Cannot read properties of undefined (reading 'on')\n    at <anonymous> (http://localhost:5174/src/App.vue:25:17)", "timestamp": "2025-08-07T12:36:11.877Z"}, {"type": "console_error", "message": "🚨 页面错误: JSHandle@error", "timestamp": "2025-08-07T12:36:15.809Z"}, {"type": "page_error", "message": "Cannot read properties of undefined (reading 'on')", "stack": "TypeError: Cannot read properties of undefined (reading 'on')\n    at <anonymous> (http://localhost:5174/src/App.vue:25:17)", "timestamp": "2025-08-07T12:36:15.810Z"}, {"type": "console_error", "message": "🚨 页面错误: JSHandle@error", "timestamp": "2025-08-07T12:36:19.938Z"}, {"type": "page_error", "message": "Cannot read properties of undefined (reading 'on')", "stack": "TypeError: Cannot read properties of undefined (reading 'on')\n    at <anonymous> (http://localhost:5174/src/App.vue:25:17)", "timestamp": "2025-08-07T12:36:19.938Z"}], "warnings": [], "pages": {"homepage": {"status": "success", "loadTime": 2643, "title": "量化投资平台", "hasContent": false, "screenshot": "homepage-1754570144014.png"}, "navigation": {"status": "success", "totalLinks": 0, "links": []}, "/dashboard": {"status": "success", "loadTime": 1039, "title": "量化投资平台", "hasContent": false, "contentLength": 0}, "/trading": {"status": "success", "loadTime": 842, "title": "量化投资平台", "hasContent": false, "contentLength": 0}, "/strategy": {"status": "success", "loadTime": 836, "title": "量化投资平台", "hasContent": false, "contentLength": 0}, "/backtest": {"status": "success", "loadTime": 763, "title": "量化投资平台", "hasContent": false, "contentLength": 0}, "/market": {"status": "success", "loadTime": 816, "title": "量化投资平台", "hasContent": false, "contentLength": 0}, "/portfolio": {"status": "success", "loadTime": 1085, "title": "量化投资平台", "hasContent": false, "contentLength": 0}, "/risk": {"status": "success", "loadTime": 1041, "title": "量化投资平台", "hasContent": false, "contentLength": 0}, "/settings": {"status": "success", "loadTime": 919, "title": "量化投资平台", "hasContent": false, "contentLength": 0}, "interactive": {"status": "success", "totalButtons": 0, "clickableButtons": 0, "totalInputs": 0, "workingInputs": 0}}, "performance": {"homepage": 2643, "domContentLoaded": 0.10000000009313226, "loadComplete": 0.6999999997206032, "firstPaint": 0, "firstContentfulPaint": 0, "resourceCount": 39}, "summary": {"successRate": "100.00%", "totalErrors": 22, "totalWarnings": 0, "testDuration": "2025-08-07T12:36:20.457Z"}}