import type { RouteRecordRaw } from 'vue-router'

/**
 * 安全的路由配置
 * 包含错误处理和降级方案
 */

// 创建带有错误处理的组件导入函数
function safeImport(importFn: () => Promise<any>, fallbackTitle: string, fallbackIcon: string = '📄') {
  return importFn().catch((error) => {
    console.warn(`组件导入失败: ${fallbackTitle}`, error)
    return {
      template: `
        <div style="
          padding: 2rem;
          text-align: center;
          background: #f9fafb;
          border-radius: 8px;
          margin: 1rem;
        ">
          <div style="font-size: 3rem; margin-bottom: 1rem;">${fallbackIcon}</div>
          <h1 style="color: #374151; margin-bottom: 1rem;">${fallbackTitle}</h1>
          <p style="color: #6b7280; margin-bottom: 2rem;">页面组件正在维护中，请稍后访问</p>
          <button
            onclick="window.location.reload()"
            style="
              background: #3b82f6;
              color: white;
              border: none;
              padding: 0.75rem 2rem;
              border-radius: 6px;
              cursor: pointer;
              margin: 0.5rem;
            "
          >
            刷新页面
          </button>
          <a
            href="/dashboard"
            style="
              display: inline-block;
              color: #6b7280;
              text-decoration: none;
              padding: 0.75rem 2rem;
              border: 1px solid #d1d5db;
              border-radius: 6px;
              margin: 0.5rem;
            "
          >
            返回首页
          </a>
        </div>
      `
    }
  })
}

export const safeRoutes: RouteRecordRaw[] = [
  {
    path: '/',
    component: () => safeImport(
      () => import('@/layouts/SimpleLayout.vue'),
      '主布局',
      '🏠'
    ),
    redirect: '/dashboard',
    children: [
      // Dashboard
      {
        path: 'dashboard',
        name: 'dashboard',
        component: () => safeImport(
          () => import('@/views/Dashboard/SimpleDashboard.vue'),
          '投资仪表盘',
          '📊'
        ),
        meta: {
          title: '投资仪表盘',
          requiresAuth: false
        }
      },

      // Market
      {
        path: 'market',
        name: 'market',
        component: () => safeImport(
          () => import('@/views/Market/MarketView.vue'),
          '市场行情',
          '📈'
        ),
        meta: {
          title: '市场行情',
          requiresAuth: false
        }
      },

      // Trading
      {
        path: 'trading',
        name: 'trading',
        component: () => safeImport(
          () => import('@/views/Trading/TradingView.vue'),
          '交易功能',
          '💰'
        ),
        meta: {
          title: '交易功能',
          requiresAuth: false
        }
      },

      // Strategy
      {
        path: 'strategy',
        name: 'strategy',
        component: () => safeImport(
          () => import('@/views/Strategy/StrategyView.vue'),
          '策略管理',
          '🧠'
        ),
        meta: {
          title: '策略管理',
          requiresAuth: false
        }
      },

      // Backtest
      {
        path: 'backtest',
        name: 'backtest',
        component: () => safeImport(
          () => import('@/views/Backtest/BacktestView.vue'),
          '策略回测',
          '🔄'
        ),
        meta: {
          title: '策略回测',
          requiresAuth: false
        }
      },

      // Portfolio
      {
        path: 'portfolio',
        name: 'portfolio',
        component: () => safeImport(
          () => import('@/views/Portfolio/PortfolioView.vue'),
          '投资组合',
          '📋'
        ),
        meta: {
          title: '投资组合',
          requiresAuth: false
        }
      },

      // Risk
      {
        path: 'risk',
        name: 'risk',
        component: () => safeImport(
          () => import('@/views/Risk/RiskView.vue'),
          '风险管理',
          '🛡️'
        ),
        meta: {
          title: '风险管理',
          requiresAuth: false
        }
      },

      // Settings
      {
        path: 'settings',
        name: 'settings',
        component: () => safeImport(
          () => import('@/views/Settings/SettingsView.vue'),
          '系统设置',
          '⚙️'
        ),
        meta: {
          title: '系统设置',
          requiresAuth: false
        }
      },

      // Safe Test
      {
        path: 'safe-test',
        name: 'safe-test',
        component: () => safeImport(
          () => import('@/views/Test/SafeTest.vue'),
          '安全测试',
          '🧪'
        ),
        meta: {
          title: '安全测试',
          requiresAuth: false
        }
      }
    ]
  },

  // 404 页面
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => Promise.resolve({
      template: `
        <div style="
          min-height: 60vh;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          text-align: center;
          padding: 2rem;
        ">
          <div style="font-size: 6rem; margin-bottom: 2rem;">🔍</div>
          <h1 style="color: #dc2626; margin-bottom: 1rem;">404 - 页面未找到</h1>
          <p style="color: #6b7280; margin-bottom: 2rem; max-width: 500px;">
            抱歉，您访问的页面不存在。可能已被移动、删除或您输入了错误的链接。
          </p>
          <div>
            <button
              onclick="window.history.back()"
              style="
                background: #6b7280;
                color: white;
                border: none;
                padding: 0.75rem 2rem;
                border-radius: 6px;
                cursor: pointer;
                margin: 0.5rem;
              "
            >
              返回上页
            </button>
            <a
              href="/dashboard"
              style="
                display: inline-block;
                background: #3b82f6;
                color: white;
                text-decoration: none;
                padding: 0.75rem 2rem;
                border-radius: 6px;
                margin: 0.5rem;
              "
            >
              返回首页
            </a>
          </div>
        </div>
      `
    }),
    meta: {
      title: '页面未找到',
      requiresAuth: false
    }
  }
]
