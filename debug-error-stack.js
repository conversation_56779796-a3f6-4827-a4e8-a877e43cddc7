/**
 * 调试错误堆栈 - 获取详细的错误信息
 */

import puppeteer from 'puppeteer';

class ErrorStackDebugger {
  constructor() {
    this.browser = null;
    this.page = null;
    this.errors = [];
  }

  async init() {
    console.log('🔍 启动错误堆栈调试...');

    this.browser = await puppeteer.launch({
      headless: false,
      defaultViewport: { width: 1920, height: 1080 },
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
      devtools: true // 打开开发者工具
    });

    this.page = await this.browser.newPage();

    // 监听所有控制台消息，包括详细的错误堆栈
    this.page.on('console', msg => {
      const type = msg.type();
      const text = msg.text();

      console.log(`[${type.toUpperCase()}] ${text}`);

      if (type === 'error') {
        this.errors.push({
          text,
          location: msg.location(),
          args: msg.args()
        });
      }
    });

    // 监听页面错误，获取详细堆栈
    this.page.on('pageerror', error => {
      console.error('❌ 页面错误详情:');
      console.error('   消息:', error.message);
      console.error('   堆栈:', error.stack);
      console.error('   名称:', error.name);

      this.errors.push({
        message: error.message,
        stack: error.stack,
        name: error.name,
        type: 'pageerror'
      });
    });

    // 监听未捕获的异常
    this.page.evaluateOnNewDocument(() => {
      window.addEventListener('error', (event) => {
        console.error('🚨 全局错误事件:', {
          message: event.message,
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
          error: event.error,
          stack: event.error ? event.error.stack : 'No stack'
        });
      });

      window.addEventListener('unhandledrejection', (event) => {
        console.error('🚨 未处理的Promise拒绝:', {
          reason: event.reason,
          promise: event.promise
        });
      });
    });
  }

  async debugApplication() {
    console.log('\n🔍 开始调试应用...');

    try {
      // 导航到首页
      console.log('1. 加载首页...');
      await this.page.goto('http://localhost:5173', {
        waitUntil: 'domcontentloaded',
        timeout: 30000
      });

      // 等待一段时间让错误发生
      console.log('2. 等待错误发生...');
      await new Promise(resolve => setTimeout(resolve, 5000));

      // 在页面中执行调试代码
      const debugInfo = await this.page.evaluate(() => {
        // 检查是否有全局错误
        const errors = window.errors || [];

        // 检查所有脚本标签
        const scripts = Array.from(document.querySelectorAll('script')).map(script => ({
          src: script.src,
          type: script.type,
          hasError: script.onerror !== null
        }));

        // 检查模块加载状态
        const moduleStatus = {
          vueLoaded: typeof window.Vue !== 'undefined',
          createAppLoaded: typeof window.createApp !== 'undefined',
          hasVueDevtools: window.__VUE_DEVTOOLS_GLOBAL_HOOK__ !== undefined
        };

        // 尝试手动执行一些代码来触发错误
        let manualTestResult = null;
        try {
          // 这里可能会触发错误
          if (window.websocketService) {
            window.websocketService.on('test', () => {});
          }
        } catch (error) {
          manualTestResult = {
            error: error.message,
            stack: error.stack
          };
        }

        return {
          errors,
          scripts,
          moduleStatus,
          manualTestResult,
          userAgent: navigator.userAgent,
          url: window.location.href
        };
      });

      console.log('\n📊 调试信息:');
      console.log('   脚本数量:', debugInfo.scripts.length);
      console.log('   Vue状态:', debugInfo.moduleStatus);
      console.log('   手动测试结果:', debugInfo.manualTestResult);

      if (debugInfo.scripts.length > 0) {
        console.log('\n📜 加载的脚本:');
        debugInfo.scripts.forEach((script, index) => {
          console.log(`   ${index + 1}. ${script.src || 'inline'} (${script.type})`);
        });
      }

      // 等待更长时间以捕获更多错误
      console.log('\n3. 继续等待更多错误...');
      await new Promise(resolve => setTimeout(resolve, 10000));

      // 分析收集到的错误
      this.analyzeErrors();

    } catch (error) {
      console.error('❌ 调试过程失败:', error.message);
    }
  }

  analyzeErrors() {
    console.log('\n🔍 错误分析:');
    console.log('='.repeat(60));

    if (this.errors.length === 0) {
      console.log('✅ 没有发现错误');
      return;
    }

    console.log(`📈 总错误数: ${this.errors.length}`);

    this.errors.forEach((error, index) => {
      console.log(`\n❌ 错误 ${index + 1}:`);

      if (error.type === 'pageerror') {
        console.log(`   类型: 页面错误`);
        console.log(`   消息: ${error.message}`);
        console.log(`   名称: ${error.name}`);
        if (error.stack) {
          console.log(`   堆栈:`);
          error.stack.split('\n').forEach(line => {
            console.log(`     ${line}`);
          });
        }
      } else {
        console.log(`   类型: 控制台错误`);
        console.log(`   消息: ${error.text}`);
        if (error.location) {
          console.log(`   位置: ${JSON.stringify(error.location)}`);
        }
      }
    });

    // 分析错误模式
    const errorPatterns = this.errors.map(e => e.message || e.text).filter(Boolean);
    const uniqueErrors = [...new Set(errorPatterns)];

    console.log(`\n🎯 唯一错误类型: ${uniqueErrors.length}`);
    uniqueErrors.forEach((error, index) => {
      console.log(`   ${index + 1}. ${error}`);
    });

    console.log('='.repeat(60));
  }

  async runDebug() {
    try {
      await this.init();
      await this.debugApplication();

      console.log('\n🎯 错误堆栈调试完成！');
      console.log('请查看浏览器开发者工具获取更多详细信息。');

      // 保持浏览器打开以便手动检查
      console.log('\n⏸️ 浏览器将保持打开状态，按任意键关闭...');

    } catch (error) {
      console.error('❌ 调试失败:', error);
    }
  }
}

// 运行调试
const errorDebugger = new ErrorStackDebugger();
errorDebugger.runDebug().catch(console.error);
